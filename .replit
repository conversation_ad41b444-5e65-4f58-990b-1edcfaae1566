language = "python3"
run = "<p><a href=\"https://github.com/affinelayer/pix2pix-tensorflow\"> [Tensorflow]</a> (by <PERSON>), <a href=\"https://github.com/Eyyub/tensorflow-pix2pix\">[Tensorflow]</a> (by <PERSON><PERSON><PERSON><PERSON><PERSON>), <a href=\"https://github.com/datitran/face2face-demo\"> [Tensorflow (face2face)]</a> (by <PERSON><PERSON>), <a href=\"https://github.com/awjuliani/Pix2Pix-Film\"> [Tensorflow (film)]</a> (by <PERSON>), <a href=\"https://github.com/kaonashi-tyc/zi2zi\">[Tensorflow (zi2zi)]</a> (by <PERSON><PERSON>), <a href=\"https://github.com/pfnet-research/chainer-pix2pix\">[Chainer]</a> (by matty<PERSON>), <a href=\"https://github.com/tjwei/GANotebooks\">[tf/torch/keras/lasagne]</a> (by tjwei), <a href=\"https://github.com/taey16/pix2pixBEGAN.pytorch\">[Pytorch]</a> (by taey16) </p> </ul>"